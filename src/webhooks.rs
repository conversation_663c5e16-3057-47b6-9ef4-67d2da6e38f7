use crate::config::Config;
use crate::error::AppResult;
use axum::{
    extract::{Request, State},
    http::{HeaderMap, StatusCode},
    response::Json,
};
use serde::Deserialize;
use tracing::{debug, error, info, warn};

/// Linear webhook payload structure
#[derive(Debug, Deserialize)]
pub struct WebhookPayload {
    pub action: String,
    pub data: serde_json::Value,
    #[serde(rename = "type")]
    pub webhook_type: Option<String>,
    pub url: Option<String>,
    pub actor: Option<Actor>,
    pub created_at: Option<String>,
}

/// Actor information from webhook
#[derive(Debug, Deserialize)]
pub struct Actor {
    pub id: String,
    pub name: String,
    #[serde(rename = "type")]
    pub actor_type: Option<String>,
}

/// Issue assignment data structure
#[derive(Debug, Deserialize)]
pub struct IssueAssignmentData {
    pub id: String,
    pub title: Option<String>,
    pub identifier: Option<String>,
    pub assignee: Option<Assignee>,
    pub team: Option<Team>,
    pub state: Option<IssueState>,
}

/// Assignee information
#[derive(Debug, Deserialize)]
pub struct Assignee {
    pub id: String,
    pub name: String,
    pub email: Option<String>,
}

/// Team information
#[derive(Debug, Deserialize)]
pub struct Team {
    pub id: String,
    pub name: String,
    pub key: String,
}

/// Issue state information
#[derive(Debug, Deserialize)]
pub struct IssueState {
    pub id: String,
    pub name: String,
    #[serde(rename = "type")]
    pub state_type: Option<String>,
}

/// Webhook service for handling Linear webhooks
pub struct WebhookService {
    config: Config,
}

impl WebhookService {
    pub fn new(config: Config) -> Self {
        Self { config }
    }

    /// Verify webhook signature from Linear
    pub fn verify_signature(&self, _payload: &[u8], signature: &str) -> AppResult<()> {
        // For now, just log that we received a webhook
        // TODO: Implement proper HMAC-SHA256 signature verification
        debug!("Webhook signature verification - signature: {}", signature);
        debug!(
            "Webhook secret configured: {}",
            !self.config.linear_webhook_secret.is_empty()
        );

        // Skip verification for now, but log it
        warn!("Webhook signature verification is not yet implemented - accepting all webhooks");
        Ok(())
    }

    /// Handle assign action webhook
    pub async fn handle_assign_action(&self, payload: &WebhookPayload) -> AppResult<()> {
        info!("🎯 ASSIGN ACTION RECEIVED!");

        // Log basic webhook information
        info!("Webhook action: {}", payload.action);
        if let Some(actor) = &payload.actor {
            info!("Actor: {} (ID: {})", actor.name, actor.id);
        }

        // Try to parse the issue assignment data
        match serde_json::from_value::<IssueAssignmentData>(payload.data.clone()) {
            Ok(issue_data) => {
                info!("📋 Issue Assignment Details:");
                info!("  Issue ID: {}", issue_data.id);

                if let Some(identifier) = &issue_data.identifier {
                    info!("  Issue Identifier: {}", identifier);
                }

                if let Some(title) = &issue_data.title {
                    info!("  Issue Title: {}", title);
                }

                if let Some(assignee) = &issue_data.assignee {
                    info!("  Assigned to: {} (ID: {})", assignee.name, assignee.id);
                    if let Some(email) = &assignee.email {
                        info!("  Assignee Email: {}", email);
                    }
                } else {
                    info!("  Assignee: None (issue was unassigned)");
                }

                if let Some(team) = &issue_data.team {
                    info!("  Team: {} ({})", team.name, team.key);
                }

                if let Some(state) = &issue_data.state {
                    info!("  State: {}", state.name);
                }
            }
            Err(e) => {
                warn!("Failed to parse issue assignment data: {}", e);
                debug!(
                    "Raw webhook data: {}",
                    serde_json::to_string_pretty(&payload.data).unwrap_or_default()
                );
            }
        }

        info!("✅ Assign action processing completed");
        Ok(())
    }

    /// Process incoming webhook
    pub async fn process_webhook(&self, payload: WebhookPayload) -> AppResult<()> {
        info!("📨 Processing webhook: action={}", payload.action);

        match &payload.webhook_type.as_ref() {
            Some(webhook_type) if *webhook_type == "Issue" => {
                info!("Webhook type: {}", webhook_type);
            }

            _ => {
                warn!(
                    "Ignoring not supported webhook_type: {:?}",
                    payload.webhook_type
                );
            }
        }

        match payload.action.as_str() {
            "update" => {
                self.handle_assign_action(&payload).await?;
            }
            _ => {
                debug!("Ignoring webhook action: {}", payload.action);
            }
        }

        Ok(())
    }
}

/// Main webhook handler endpoint
pub async fn webhook_handler(
    State(app_state): State<crate::AppState>,
    headers: HeaderMap,
    request: Request,
) -> Result<Json<serde_json::Value>, (StatusCode, String)> {
    // Extract the body
    let body = match axum::body::to_bytes(request.into_body(), usize::MAX).await {
        Ok(bytes) => bytes,
        Err(e) => {
            error!("Failed to read webhook body: {}", e);
            return Err((
                StatusCode::BAD_REQUEST,
                "Failed to read request body".to_string(),
            ));
        }
    };

    // Get the signature header
    let signature = headers
        .get("linear-signature")
        .and_then(|h| h.to_str().ok())
        .unwrap_or("");

    // Verify signature
    if let Err(e) = app_state.webhook_service.verify_signature(&body, signature) {
        error!("Webhook signature verification failed: {}", e);
        return Err((StatusCode::UNAUTHORIZED, "Invalid signature".to_string()));
    }

    // Parse the webhook payload
    let payload: WebhookPayload = match serde_json::from_slice(&body) {
        Ok(payload) => payload,
        Err(e) => {
            error!("Failed to parse webhook payload: {}", e);
            return Err((StatusCode::BAD_REQUEST, "Invalid JSON payload".to_string()));
        }
    };

    // Process the webhook
    if let Err(e) = app_state.webhook_service.process_webhook(payload).await {
        error!("Failed to process webhook: {}", e);
        return Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            "Webhook processing failed".to_string(),
        ));
    }

    // Return success response
    Ok(Json(serde_json::json!({
        "status": "success",
        "message": "Webhook processed successfully"
    })))
}
