use crate::error::{AppError, AppR<PERSON>ult};
use std::env;

/// Application configuration loaded from environment variables
#[derive(Debug, Clone)]
pub struct Config {
    /// Linear OAuth app client ID
    pub linear_client_id: String,

    /// Linear OAuth app client secret
    pub linear_client_secret: String,

    /// Linear webhook secret for signature verification
    pub linear_webhook_secret: String,

    /// OAuth callback/redirect URI
    pub linear_redirect_uri: String,

    /// Server port configuration
    pub server_port: u16,

    /// Logging level configuration
    pub log_level: String,
}

impl Config {
    /// Load configuration from environment variables
    pub fn from_env() -> AppResult<Self> {
        // Load .env file if it exists (ignore errors for production deployments)
        let _ = dotenvy::dotenv();

        let config = Config {
            linear_client_id: get_env_var("LINEAR_CLIENT_ID")?,
            linear_client_secret: get_env_var("LINEAR_CLIENT_SECRET")?,
            linear_webhook_secret: get_env_var("LINEAR_WEBHOOK_SECRET")?,
            linear_redirect_uri: get_env_var("LINEAR_REDIRECT_URI")?,
            server_port: get_env_var("SERVER_PORT")
                .and_then(|s| {
                    s.parse().map_err(|e| AppError::InvalidEnvVar {
                        var: "SERVER_PORT".to_string(),
                        reason: format!("must be a valid port number: {}", e),
                    })
                })
                .unwrap_or(3000), // Default to port 3000
            log_level: env::var("LOG_LEVEL").unwrap_or_else(|_| "info".to_string()),
        };

        Ok(config)
    }

    /// Get the server bind address
    pub fn bind_address(&self) -> String {
        format!("0.0.0.0:{}", self.server_port)
    }
}

/// Helper function to get required environment variable
fn get_env_var(var_name: &str) -> AppResult<String> {
    env::var(var_name).map_err(|_| AppError::MissingEnvVar {
        var: var_name.to_string(),
    })
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_get_env_var_missing() {
        let result = get_env_var("NONEXISTENT_VAR_12345");
        assert!(result.is_err());
        match result {
            Err(AppError::MissingEnvVar { var }) => {
                assert_eq!(var, "NONEXISTENT_VAR_12345");
            }
            _ => panic!("Expected MissingEnvVar error"),
        }
    }

    #[test]
    fn test_bind_address() {
        let config = Config {
            linear_client_id: "test".to_string(),
            linear_client_secret: "test".to_string(),
            linear_webhook_secret: "test".to_string(),
            linear_redirect_uri: "test".to_string(),
            server_port: 8080,
            log_level: "info".to_string(),
        };

        assert_eq!(config.bind_address(), "0.0.0.0:8080");
    }
}
