use axum::{
    Json,
    http::StatusCode,
    response::{IntoResponse, Response},
};
use serde_json::json;
use thiserror::Error;

/// Application-specific error types
#[derive(Error, Debug)]
pub enum AppError {
    #[error("Configuration error: {0}")]
    Config(String),

    #[error("Environment variable '{var}' is missing")]
    MissingEnvVar { var: String },

    #[error("Environment variable '{var}' has invalid value: {reason}")]
    InvalidEnvVar { var: String, reason: String },

    #[error("Server error: {0}")]
    Server(#[from] std::io::Error),

    #[error("Internal error: {0}")]
    Internal(#[from] anyhow::Error),

    #[error("OAuth error: {0}")]
    OAuth(String),

    #[error("HTTP request error: {0}")]
    Http(#[from] reqwest::Error),

    #[error("URL parsing error: {0}")]
    UrlParse(#[from] url::ParseError),

    #[error("JSON parsing error: {0}")]
    Json(#[from] serde_json::Error),
}

/// Result type alias for convenience
pub type AppResult<T> = Result<T, AppError>;

impl IntoResponse for AppError {
    fn into_response(self) -> Response {
        let (status, error_message) = match self {
            AppError::Config(_) => (StatusCode::INTERNAL_SERVER_ERROR, self.to_string()),
            AppError::MissingEnvVar { .. } => (StatusCode::INTERNAL_SERVER_ERROR, self.to_string()),
            AppError::InvalidEnvVar { .. } => (StatusCode::INTERNAL_SERVER_ERROR, self.to_string()),
            AppError::Server(_) => (StatusCode::INTERNAL_SERVER_ERROR, self.to_string()),
            AppError::Internal(_) => (StatusCode::INTERNAL_SERVER_ERROR, self.to_string()),
            AppError::OAuth(_) => (StatusCode::BAD_REQUEST, self.to_string()),
            AppError::Http(_) => (StatusCode::BAD_GATEWAY, self.to_string()),
            AppError::UrlParse(_) => (StatusCode::BAD_REQUEST, self.to_string()),
            AppError::Json(_) => (StatusCode::BAD_REQUEST, self.to_string()),
        };

        let body = Json(json!({
            "error": error_message,
        }));

        (status, body).into_response()
    }
}
