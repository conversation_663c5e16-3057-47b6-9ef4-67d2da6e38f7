use axum::{Router, routing::get};
use std::sync::Arc;
use tracing::info;
use tracing_subscriber;

mod config;
mod error;
mod oauth;

use config::Config;
use error::AppResult;
use oauth::OAuthService;

#[tokio::main]
async fn main() -> AppResult<()> {
    // Load configuration from environment
    let config = Config::from_env()?;

    // Initialize logging
    init_logging(&config.log_level);

    info!("Starting Linear Shape-Up Bot server");
    info!("Configuration loaded successfully");

    // Initialize OAuth service
    let oauth_service = Arc::new(OAuthService::new(config.clone()));
    info!("OAuth service initialized");

    let app = Router::new()
        .route("/", get(|| async { "Linear Shape-Up Bot is running!" }))
        .route("/health", get(health_check))
        .route("/auth", get(oauth::auth_handler))
        .route("/auth/callback", get(oauth::callback_handler))
        .route("/auth/status", get(oauth::status_handler))
        .with_state(oauth_service);

    let addr = config.bind_address();
    info!("Server listening on http://{}", addr);

    // Run our app with hyper
    let listener = tokio::net::TcpListener::bind(&addr).await?;
    axum::serve(listener, app).await?;

    Ok(())
}

/// Initialize tracing/logging based on the configured log level
fn init_logging(log_level: &str) {
    let level = match log_level.to_lowercase().as_str() {
        "error" => tracing::Level::ERROR,
        "warn" => tracing::Level::WARN,
        "info" => tracing::Level::INFO,
        "debug" => tracing::Level::DEBUG,
        "trace" => tracing::Level::TRACE,
        _ => tracing::Level::INFO,
    };

    tracing_subscriber::fmt()
        .with_max_level(level)
        .with_target(false)
        .with_file(true)
        .with_line_number(true)
        .init();
}

/// Health check endpoint
async fn health_check() -> &'static str {
    "OK"
}
