use axum::{Router, routing::{get, post}};
use std::sync::Arc;
use tracing::info;
use tracing_subscriber;

mod config;
mod error;
mod oauth;
mod webhooks;

use config::Config;
use error::AppResult;
use oauth::OAuthService;
use webhooks::WebhookService;

/// Shared application state
#[derive(Clone)]
pub struct AppState {
    pub oauth_service: Arc<OAuthService>,
    pub webhook_service: Arc<WebhookService>,
}

#[tokio::main]
async fn main() -> AppResult<()> {
    // Load configuration from environment
    let config = Config::from_env()?;

    // Initialize logging
    init_logging(&config.log_level);

    info!("Starting Linear Shape-Up Bot server");
    info!("Configuration loaded successfully");

    // Initialize services
    let oauth_service = Arc::new(OAuthService::new(config.clone()));
    let webhook_service = Arc::new(WebhookService::new(config.clone()));
    info!("OAuth and Webhook services initialized");

    // Create shared application state
    let app_state = AppState {
        oauth_service,
        webhook_service,
    };

    let app = Router::new()
        .route("/", get(|| async { "Linear Shape-Up Bot is running!" }))
        .route("/health", get(health_check))
        .route("/auth", get(oauth::auth_handler))
        .route("/auth/callback", get(oauth::callback_handler))
        .route("/auth/status", get(oauth::status_handler))
        .route("/webhook", post(webhooks::webhook_handler))
        .with_state(app_state);

    let addr = config.bind_address();
    info!("Server listening on http://{}", addr);

    // Run our app with hyper
    let listener = tokio::net::TcpListener::bind(&addr).await?;
    axum::serve(listener, app).await?;

    Ok(())
}

/// Initialize tracing/logging based on the configured log level
fn init_logging(log_level: &str) {
    let level = match log_level.to_lowercase().as_str() {
        "error" => tracing::Level::ERROR,
        "warn" => tracing::Level::WARN,
        "info" => tracing::Level::INFO,
        "debug" => tracing::Level::DEBUG,
        "trace" => tracing::Level::TRACE,
        _ => tracing::Level::INFO,
    };

    tracing_subscriber::fmt()
        .with_max_level(level)
        .with_target(false)
        .with_file(true)
        .with_line_number(true)
        .init();
}

/// Health check endpoint
async fn health_check() -> &'static str {
    "OK"
}
