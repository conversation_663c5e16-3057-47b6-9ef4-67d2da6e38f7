use crate::config::Config;
use crate::error::{AppError, AppResult};
use axum::{
    <PERSON><PERSON>,
    extract::{Query, State},
    response::{Html, Redirect},
};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use url::Url;
use uuid::Uuid;

/// OAuth state parameter for CSRF protection
#[derive(Debug, Clone)]
pub struct OAuthState {
    pub state_id: String,
    pub created_at: DateTime<Utc>,
}

/// OAuth access token information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccessToken {
    pub access_token: String,
    pub token_type: String,
    pub expires_in: i64,
    pub scope: String,
    pub created_at: DateTime<Utc>,
}

impl AccessToken {
    /// Check if the token is expired
    pub fn is_expired(&self) -> bool {
        let expiry = self.created_at + chrono::Duration::seconds(self.expires_in);
        Utc::now() > expiry
    }
}

/// OAuth callback query parameters
#[derive(Debug, Deserialize)]
pub struct OAuthCallback {
    pub code: String,
    pub state: String,
}

/// OAuth token exchange request
#[derive(Debug, Serialize)]
struct TokenRequest {
    code: String,
    redirect_uri: String,
    client_id: String,
    client_secret: String,
    grant_type: String,
}

/// OAuth token exchange response
#[derive(Debug, Deserialize)]
struct TokenResponse {
    access_token: String,
    token_type: String,
    expires_in: i64,
    scope: String,
}

/// In-memory storage for OAuth states and tokens
#[derive(Debug, Clone)]
pub struct OAuthStorage {
    states: Arc<Mutex<HashMap<String, OAuthState>>>,
    tokens: Arc<Mutex<HashMap<String, AccessToken>>>,
}

impl OAuthStorage {
    pub fn new() -> Self {
        Self {
            states: Arc::new(Mutex::new(HashMap::new())),
            tokens: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    /// Store OAuth state for CSRF protection
    pub fn store_state(&self, state: OAuthState) {
        let mut states = self.states.lock().unwrap();
        states.insert(state.state_id.clone(), state);
    }

    /// Verify and consume OAuth state
    pub fn verify_state(&self, state_id: &str) -> AppResult<()> {
        let mut states = self.states.lock().unwrap();
        match states.remove(state_id) {
            Some(state) => {
                // Check if state is not too old (5 minutes max)
                let age = Utc::now() - state.created_at;
                if age > chrono::Duration::minutes(5) {
                    return Err(AppError::OAuth("OAuth state expired".to_string()));
                }
                Ok(())
            }
            None => Err(AppError::OAuth("Invalid OAuth state".to_string())),
        }
    }

    /// Store access token
    pub fn store_token(&self, user_id: String, token: AccessToken) {
        let mut tokens = self.tokens.lock().unwrap();
        tokens.insert(user_id, token);
    }

    /// Get access token for user
    pub fn get_token(&self, user_id: &str) -> Option<AccessToken> {
        let tokens = self.tokens.lock().unwrap();
        tokens.get(user_id).cloned()
    }
}

/// OAuth service for handling Linear OAuth flow
pub struct OAuthService {
    config: Config,
    storage: OAuthStorage,
    http_client: reqwest::Client,
}

impl OAuthService {
    pub fn new(config: Config) -> Self {
        Self {
            config,
            storage: OAuthStorage::new(),
            http_client: reqwest::Client::new(),
        }
    }

    /// Generate authorization URL for Linear OAuth
    pub fn generate_auth_url(&self) -> AppResult<(String, String)> {
        let state_id = Uuid::new_v4().to_string();
        let state = OAuthState {
            state_id: state_id.clone(),
            created_at: Utc::now(),
        };

        // Store state for later verification
        self.storage.store_state(state);

        let mut auth_url = Url::parse("https://linear.app/oauth/authorize")?;
        auth_url
            .query_pairs_mut()
            .append_pair("client_id", &self.config.linear_client_id)
            .append_pair("redirect_uri", &self.config.linear_redirect_uri)
            .append_pair("response_type", "code")
            .append_pair("scope", "read,write,app:mentionable,app:assignable")
            .append_pair("state", &state_id)
            .append_pair("actor", "app"); // Use app actor for agent behavior

        Ok((auth_url.to_string(), state_id))
    }

    /// Exchange authorization code for access token
    pub async fn exchange_code(&self, code: String, state: String) -> AppResult<AccessToken> {
        // Verify state parameter
        self.storage.verify_state(&state)?;

        let token_request = TokenRequest {
            code,
            redirect_uri: self.config.linear_redirect_uri.clone(),
            client_id: self.config.linear_client_id.clone(),
            client_secret: self.config.linear_client_secret.clone(),
            grant_type: "authorization_code".to_string(),
        };

        let response = self
            .http_client
            .post("https://api.linear.app/oauth/token")
            .header("Content-Type", "application/x-www-form-urlencoded")
            .form(&token_request)
            .send()
            .await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(AppError::OAuth(format!(
                "Token exchange failed: {}",
                error_text
            )));
        }

        let token_response: TokenResponse = response.json().await?;

        let access_token = AccessToken {
            access_token: token_response.access_token,
            token_type: token_response.token_type,
            expires_in: token_response.expires_in,
            scope: token_response.scope,
            created_at: Utc::now(),
        };

        Ok(access_token)
    }

    /// Get storage reference for sharing with handlers
    pub fn storage(&self) -> &OAuthStorage {
        &self.storage
    }
}

/// Handler for initiating OAuth flow
pub async fn auth_handler(State(oauth_service): State<Arc<OAuthService>>) -> AppResult<Redirect> {
    let (auth_url, _state_id) = oauth_service.generate_auth_url()?;
    Ok(Redirect::temporary(&auth_url))
}

/// Handler for OAuth callback
pub async fn callback_handler(
    Query(params): Query<OAuthCallback>,
    State(oauth_service): State<Arc<OAuthService>>,
) -> AppResult<Html<String>> {
    match oauth_service.exchange_code(params.code, params.state).await {
        Ok(token) => {
            // For now, store token with a generic user ID
            // In a real implementation, you'd get the user ID from the token
            let user_id = "default_user".to_string();
            oauth_service.storage().store_token(user_id, token);

            Ok(Html(
                r#"
                <html>
                    <body>
                        <h1>✅ OAuth Success!</h1>
                        <p>Successfully authenticated with Linear. You can now close this window.</p>
                        <script>
                            setTimeout(() => window.close(), 3000);
                        </script>
                    </body>
                </html>
                "#
                .to_string(),
            ))
        }
        Err(e) => Ok(Html(format!(
            r#"
            <html>
                <body>
                    <h1>❌ OAuth Error</h1>
                    <p>Authentication failed: {}</p>
                </body>
            </html>
            "#,
            e
        ))),
    }
}

/// Handler to check OAuth status
pub async fn status_handler(
    State(oauth_service): State<Arc<OAuthService>>,
) -> Json<serde_json::Value> {
    let user_id = "default_user";
    match oauth_service.storage().get_token(user_id) {
        Some(token) if !token.is_expired() => Json(serde_json::json!({
            "authenticated": true,
            "expires_at": token.created_at + chrono::Duration::seconds(token.expires_in),
            "scope": token.scope
        })),
        _ => Json(serde_json::json!({
            "authenticated": false
        })),
    }
}
