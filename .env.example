# Linear Shape-Up Bot Configuration
# Copy this file to .env and fill in your actual values

# Linear OAuth App Configuration
# Get these from your Linear OAuth app settings
LINEAR_CLIENT_ID=your_linear_client_id_here
LINEAR_CLIENT_SECRET=your_linear_client_secret_here

# Linear Webhook Configuration  
# This secret is used to verify webhook signatures from Linear
LINEAR_WEBHOOK_SECRET=your_webhook_secret_here

# OAuth Redirect URI
# This should match the redirect URI configured in your Linear OAuth app
# For local development: http://localhost:3000/auth/callback
LINEAR_REDIRECT_URI=http://localhost:3000/auth/callback

# Server Configuration
# Port for the web server to listen on
SERVER_PORT=3000

# Logging Configuration
# Options: error, warn, info, debug, trace
LOG_LEVEL=info
