# Linear Shape-Up Bot

A Rust-based Linear agent that helps frame feature requests using <PERSON>'s Shape Up methodology.

## Current Status

✅ **Step 1.1 - Environment Configuration & Security** (COMPLETED)

The foundation infrastructure is now in place:

### What's Implemented

- **Configuration Management**: Environment variable handling with `.env` file support
- **Error Handling**: Custom error types with proper error propagation
- **Logging**: Structured logging with configurable levels
- **HTTP Server**: Basic Axum web server with health check endpoint
- **Testing**: Unit tests for configuration functionality

### Project Structure

```
src/
├── main.rs              # Server setup and routing
├── config.rs            # Environment configuration management
├── error.rs             # Error types and handling
└── ...                  # Future modules (oauth.rs, webhooks.rs, etc.)
```

### Configuration

The application requires the following environment variables:

- `LINEAR_CLIENT_ID` - Linear OAuth app client ID
- `LINEAR_CLIENT_SECRET` - Linear OAuth app client secret  
- `LINEAR_WEBHOOK_SECRET` - Webhook signature verification secret
- `LINEAR_REDIRECT_URI` - OAuth callback URL
- `SERVER_PORT` - Server port (defaults to 3000)
- `LOG_LEVEL` - Logging level (defaults to "info")

Copy `.env.example` to `.env` and fill in your values.

## Getting Started

### Prerequisites

- Rust 1.70+ 
- Linear OAuth app configured

### Running the Server

```bash
# Install dependencies and run
cargo run

# Run tests
cargo test

# Build for production
cargo build --release
```

The server will start on `http://localhost:3000` (or the configured port).

### Endpoints

- `GET /` - Basic status message
- `GET /health` - Health check endpoint

## Next Steps

The following steps from the implementation plan are ready to be implemented:

- **1.2 OAuth 2.0 Authentication** - Linear OAuth flow handlers
- **1.3 Linear API Client** - GraphQL client for Linear API
- **1.4 Webhook Infrastructure** - Webhook handling and signature verification
- **1.5 Agent Session Management** - Session state and lifecycle management

## Development

### Adding Dependencies

Use Cargo to add new dependencies:

```bash
cargo add <dependency-name>
```

### Code Structure

The codebase follows a modular structure with clear separation of concerns:

- Configuration and environment handling in `config.rs`
- Error types and handling in `error.rs`
- HTTP routing and server setup in `main.rs`

### Testing

Run tests with:

```bash
cargo test
```

Tests are co-located with the modules they test using Rust's built-in testing framework.
