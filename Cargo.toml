[package]
name = "linear-shape-up-bot"
version = "0.1.0"
edition = "2024"

[dependencies]
axum = "0.8.4"
tokio = { version = "1.18.2", features = ["full"] }
serde = { version = "1.0.136", features = ["derive"] }
serde_json = "1.0.79"

# Environment configuration
dotenvy = "0.15"

# Logging
tracing = "0.1"
tracing-subscriber = "0.3"

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# HTTP client for OAuth and Linear API
reqwest = { version = "0.11", features = ["json"] }

# URL handling for OAuth
url = "2.4"

# UUID generation for OAuth state
uuid = { version = "1.0", features = ["v4"] }

# Time handling for token expiration
chrono = { version = "0.4", features = ["serde"] }
